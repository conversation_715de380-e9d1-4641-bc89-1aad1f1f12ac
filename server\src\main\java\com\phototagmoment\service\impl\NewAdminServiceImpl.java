package com.phototagmoment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.common.ResultCode;
import com.phototagmoment.dto.*;
import com.phototagmoment.vo.AdminPermissionTreeVO;
import com.phototagmoment.entity.*;
import com.phototagmoment.exception.BusinessException;
import com.phototagmoment.mapper.*;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.NewAdminService;
import com.phototagmoment.vo.AdminVO;
import com.phototagmoment.vo.LoginResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新管理员服务实现类
 */
@Slf4j
@Service
public class NewAdminServiceImpl implements NewAdminService {

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Value("${jwt.expiration}")
    private long jwtExpiration;

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private AdminRoleMapper adminRoleMapper;

    @Autowired
    private AdminPermissionMapper adminPermissionMapper;

    @Autowired
    private AdminRolePermissionMapper adminRolePermissionMapper;

    @Autowired
    private AdminOperationLogMapper adminOperationLogMapper;

    // 不再需要 AuthenticationManager
    // @Autowired
    // private AuthenticationManager authenticationManager;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private HttpServletRequest request;

    @Override
    public LoginResultVO login(AdminLoginDTO loginDTO) {
        log.info("管理员登录服务: 用户名={}", loginDTO.getUsername());

        // 查找管理员
        Admin admin = adminMapper.selectByUsername(loginDTO.getUsername());
        if (admin == null) {
            log.error("管理员登录失败: 用户名不存在, 用户名={}", loginDTO.getUsername());
            throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
        }
        log.info("管理员查询成功: id={}, 用户名={}, 状态={}", admin.getId(), admin.getUsername(), admin.getStatus());

        // 检查管理员状态
        if (admin.getStatus() == 0) {
            log.error("管理员登录失败: 账号已禁用, 用户名={}", loginDTO.getUsername());
            throw new BusinessException(ResultCode.USER_ACCOUNT_DISABLED);
        }

        try {
            // 验证密码
            log.info("验证密码: 用户名={}", loginDTO.getUsername());
            if (!passwordEncoder.matches(loginDTO.getPassword(), admin.getPassword())) {
                log.error("管理员登录失败: 密码错误, 用户名={}", loginDTO.getUsername());
                throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
            }
            log.info("密码验证成功: 用户名={}", loginDTO.getUsername());

            // 创建认证信息
            List<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            UserDetails userDetails = new org.springframework.security.core.userdetails.User(
                    admin.getUsername(),
                    admin.getPassword(),
                    authorities
            );

            // 生成JWT
            log.info("生成JWT: 用户名={}", loginDTO.getUsername());
            String token = jwtTokenProvider.generateToken(userDetails.getUsername());
            log.info("JWT生成成功: 用户名={}, token长度={}", loginDTO.getUsername(), token.length());

            // 更新管理员最后登录时间和IP
            String ip = getIpAddress(request);
            adminMapper.updateLastLogin(admin.getId(), ip);
            log.info("更新登录信息: 用户名={}, IP={}", loginDTO.getUsername(), ip);

            // 记录登录日志
            logOperation(admin.getId(), "系统", "登录", "管理员登录系统", ip);
            log.info("记录登录日志: 用户名={}", loginDTO.getUsername());

            // 构建返回对象
            LoginResultVO loginResultVO = new LoginResultVO();
            loginResultVO.setToken(token);
            loginResultVO.setTokenType(tokenPrefix);
            loginResultVO.setExpiresIn(jwtExpiration / 1000);
            loginResultVO.setAdmin(convertToAdminVO(admin));
            log.info("登录成功: 用户名={}", loginDTO.getUsername());

            return loginResultVO;
        } catch (BusinessException e) {
            log.error("管理员登录业务异常: 用户名={}, 错误={}", loginDTO.getUsername(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("管理员登录系统异常: 用户名={}, 错误={}", loginDTO.getUsername(), e.getMessage(), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }

    @Override
    public AdminVO getAdminInfo(Long id) {
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }
        return convertToAdminVO(admin);
    }

    @Override
    public AdminVO getCurrentAdmin() {
        log.info("开始获取当前管理员信息");

        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            log.warn("认证信息为空");
            return null;
        }

        log.info("认证信息: {}", authentication);
        log.info("认证类型: {}", authentication.getClass().getName());
        log.info("认证权限: {}", authentication.getAuthorities());

        // 检查是否是匿名用户
        if (authentication.getPrincipal().equals("anonymousUser")) {
            log.warn("当前用户是匿名用户，无法获取管理员信息");
            return null;
        }

        if (!authentication.isAuthenticated()) {
            log.warn("用户未认证");
            return null;
        }

        // 获取用户名
        String username = null;
        Object principal = authentication.getPrincipal();
        log.info("Principal类型: {}", principal.getClass().getName());

        if (principal instanceof UserDetails) {
            username = ((UserDetails) principal).getUsername();
            log.info("从UserDetails中获取用户名: {}", username);
        } else if (principal instanceof String) {
            username = (String) principal;
            log.info("从String中获取用户名: {}", username);

            // 如果用户名是匿名用户，则返回null
            if ("anonymousUser".equals(username)) {
                log.warn("用户名是匿名用户，无法获取管理员信息");
                return null;
            }
        } else {
            log.warn("无法从Principal中获取用户名: {}", principal);
        }

        if (username == null) {
            log.warn("用户名为空");
            return null;
        }

        // 查询管理员
        log.info("查询管理员: {}", username);
        Admin admin = adminMapper.selectByUsername(username);
        if (admin == null) {
            log.warn("管理员不存在: {}", username);
            return null;
        }

        log.info("管理员信息: id={}, 用户名={}, 状态={}", admin.getId(), admin.getUsername(), admin.getStatus());
        AdminVO adminVO = convertToAdminVO(admin);
        log.info("管理员VO信息: id={}, 用户名={}, 角色={}", adminVO.getId(), adminVO.getUsername(), adminVO.getRoleName());

        return adminVO;
    }

    @Override
    public boolean logout() {
        // 获取当前管理员
        AdminVO adminVO = getCurrentAdmin();
        if (adminVO != null) {
            // 记录登出日志
            logOperation(adminVO.getId(), "系统", "登出", "管理员退出系统", getIpAddress(request));
        }

        // 清除认证信息
        SecurityContextHolder.clearContext();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAdmin(AdminDTO adminDTO, Long creatorId) {
        // 检查用户名是否已存在
        Admin existAdmin = adminMapper.selectByUsername(adminDTO.getUsername());
        if (existAdmin != null) {
            throw new BusinessException(ResultCode.DUPLICATE_USERNAME);
        }

        // 检查角色是否存在
        if (adminDTO.getRoleId() != null) {
            AdminRole role = adminRoleMapper.selectById(adminDTO.getRoleId());
            if (role == null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
            }
        }

        // 创建管理员
        Admin admin = new Admin();
        BeanUtil.copyProperties(adminDTO, admin);
        admin.setPassword(passwordEncoder.encode(adminDTO.getPassword()));
        admin.setStatus(adminDTO.getStatus() != null ? adminDTO.getStatus() : 1);
        admin.setCreatorId(creatorId);
        adminMapper.insert(admin);

        // 记录操作日志
        logOperation(creatorId, "管理员", "创建", "创建管理员：" + admin.getUsername(), getIpAddress(request));

        return admin.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdmin(Long id, AdminDTO adminDTO) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 检查角色是否存在
        if (adminDTO.getRoleId() != null) {
            AdminRole role = adminRoleMapper.selectById(adminDTO.getRoleId());
            if (role == null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
            }
        }

        // 更新管理员信息
        BeanUtil.copyProperties(adminDTO, admin, "username", "password");
        adminMapper.updateById(admin);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "管理员", "更新", "更新管理员信息：" + admin.getUsername(), getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAdmin(Long id) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 删除管理员
        adminMapper.deleteById(id);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "管理员", "删除", "删除管理员：" + admin.getUsername(), getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(Long id, AdminPasswordDTO passwordDTO) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 检查旧密码是否正确
        if (!passwordEncoder.matches(passwordDTO.getOldPassword(), admin.getPassword())) {
            throw new BusinessException(ResultCode.USERNAME_OR_PASSWORD_ERROR);
        }

        // 检查新密码和确认密码是否一致
        if (!passwordDTO.getNewPassword().equals(passwordDTO.getConfirmPassword())) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "两次输入的密码不一致");
        }

        // 更新密码
        admin.setPassword(passwordEncoder.encode(passwordDTO.getNewPassword()));
        adminMapper.updateById(admin);

        // 记录操作日志
        logOperation(id, "管理员", "修改密码", "管理员修改密码", getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetPassword(Long id, Long operatorId) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 生成随机密码
        String newPassword = RandomUtil.randomString(8);

        // 更新密码
        admin.setPassword(passwordEncoder.encode(newPassword));
        adminMapper.updateById(admin);

        // 记录操作日志
        logOperation(operatorId, "管理员", "重置密码", "重置管理员密码：" + admin.getUsername(), getIpAddress(request));

        return newPassword;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status, Long operatorId) {
        // 检查管理员是否存在
        Admin admin = adminMapper.selectById(id);
        if (admin == null) {
            throw new BusinessException(ResultCode.USER_NOT_EXIST);
        }

        // 更新状态
        admin.setStatus(status);
        adminMapper.updateById(admin);

        // 记录操作日志
        String statusText = status == 1 ? "启用" : "禁用";
        logOperation(operatorId, "管理员", statusText, statusText + "管理员：" + admin.getUsername(), getIpAddress(request));

        return true;
    }

    @Override
    public IPage<AdminVO> getAdminPage(int page, int size, String keyword) {
        Page<Admin> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.like(Admin::getUsername, keyword)
                    .or()
                    .like(Admin::getName, keyword)
                    .or()
                    .like(Admin::getPhone, keyword)
                    .or()
                    .like(Admin::getEmail, keyword);
        }

        queryWrapper.orderByDesc(Admin::getCreatedAt);

        // 查询管理员列表
        IPage<Admin> adminPage = adminMapper.selectPage(pageParam, queryWrapper);

        // 转换为VO
        IPage<AdminVO> adminVOPage = new Page<>(adminPage.getCurrent(), adminPage.getSize(), adminPage.getTotal());
        List<AdminVO> adminVOList = new ArrayList<>();
        for (Admin admin : adminPage.getRecords()) {
            adminVOList.add(convertToAdminVO(admin));
        }
        adminVOPage.setRecords(adminVOList);

        return adminVOPage;
    }

    @Override
    public boolean hasPermission(Long adminId, String permission) {
        // 查询管理员
        Admin admin = adminMapper.selectById(adminId);
        if (admin == null) {
            return false;
        }

        // 查询管理员权限
        List<String> permissions = getPermissions(adminId);
        return permissions.contains(permission);
    }

    @Override
    public List<String> getPermissions(Long adminId) {
        // 查询管理员
        Admin admin = adminMapper.selectById(adminId);
        if (admin == null) {
            return new ArrayList<>();
        }

        // 查询管理员角色
        AdminRole role = adminRoleMapper.selectById(admin.getRoleId());
        if (role == null) {
            return new ArrayList<>();
        }

        // 查询角色权限
        return adminPermissionMapper.selectCodesByRoleId(role.getId());
    }

    @Override
    public void logOperation(Long adminId, String module, String operation, String content, String ip) {
        // 查询管理员
        Admin admin = adminMapper.selectById(adminId);
        if (admin == null) {
            return;
        }

        // 创建操作日志
        AdminOperationLog log = new AdminOperationLog();
        log.setAdminId(adminId);
        log.setUsername(admin.getUsername());
        log.setModule(module);
        log.setOperation(operation);
        log.setContent(content);
        log.setIp(ip);
        log.setUserAgent(request.getHeader("User-Agent"));

        // 手动设置创建时间，以防自动填充不起作用
        log.setCreatedAt(LocalDateTime.now());

        adminOperationLogMapper.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRole(AdminRoleDTO roleDTO) {
        // 检查角色编码是否已存在
        AdminRole existRole = adminRoleMapper.selectByCode(roleDTO.getCode());
        if (existRole != null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色编码已存在");
        }

        // 创建角色
        AdminRole role = new AdminRole();
        BeanUtil.copyProperties(roleDTO, role);
        role.setStatus(roleDTO.getStatus() != null ? roleDTO.getStatus() : 1);
        role.setIsSystem(0);
        adminRoleMapper.insert(role);

        // 设置角色权限
        if (roleDTO.getPermissionIds() != null && !roleDTO.getPermissionIds().isEmpty()) {
            setRolePermissions(role.getId(), roleDTO.getPermissionIds());
        }

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "角色", "创建", "创建角色：" + role.getName(), getIpAddress(request));

        return role.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Long id, AdminRoleDTO roleDTO) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(id);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 检查是否是系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置角色不能修改");
        }

        // 检查角色编码是否已存在
        if (!role.getCode().equals(roleDTO.getCode())) {
            AdminRole existRole = adminRoleMapper.selectByCode(roleDTO.getCode());
            if (existRole != null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "角色编码已存在");
            }
        }

        // 更新角色
        BeanUtil.copyProperties(roleDTO, role);
        adminRoleMapper.updateById(role);

        // 更新角色权限
        if (roleDTO.getPermissionIds() != null) {
            setRolePermissions(id, roleDTO.getPermissionIds());
        }

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "角色", "更新", "更新角色：" + role.getName(), getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long id) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(id);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 检查是否是系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置角色不能删除");
        }

        // 检查是否有管理员使用该角色
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Admin::getRoleId, id);
        Long count = adminMapper.selectCount(queryWrapper);
        if (count != null && count > 0) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "该角色下有管理员，不能删除");
        }

        // 删除角色
        adminRoleMapper.deleteById(id);

        // 删除角色权限关联
        adminRolePermissionMapper.deleteByRoleId(id);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "角色", "删除", "删除角色：" + role.getName(), getIpAddress(request));

        return true;
    }

    @Override
    public List<AdminRole> getRoleList() {
        LambdaQueryWrapper<AdminRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminRole::getStatus, 1);
        queryWrapper.orderByAsc(AdminRole::getIsSystem).orderByAsc(AdminRole::getId);
        return adminRoleMapper.selectList(queryWrapper);
    }

    @Override
    public List<AdminPermission> getPermissionList() {
        LambdaQueryWrapper<AdminPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminPermission::getStatus, 1);
        queryWrapper.orderByAsc(AdminPermission::getParentId).orderByAsc(AdminPermission::getSort);
        return adminPermissionMapper.selectList(queryWrapper);
    }

    @Override
    public List<Long> getRolePermissions(Long roleId) {
        return adminRolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setRolePermissions(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(roleId);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 删除原有权限
        adminRolePermissionMapper.deleteByRoleId(roleId);

        // 添加新权限
        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<AdminRolePermission> rolePermissions = new ArrayList<>();
            for (Long permissionId : permissionIds) {
                AdminRolePermission rolePermission = new AdminRolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(permissionId);
                rolePermissions.add(rolePermission);
            }
            for (AdminRolePermission rolePermission : rolePermissions) {
                adminRolePermissionMapper.insert(rolePermission);
            }
        }

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "角色", "设置权限", "设置角色权限：" + role.getName(), getIpAddress(request));

        return true;
    }

    @Override
    public AdminVO convertToAdminVO(Admin admin) {
        if (admin == null) {
            return null;
        }

        AdminVO adminVO = new AdminVO();
        BeanUtil.copyProperties(admin, adminVO);

        // 设置角色信息
        if (admin.getRoleId() != null) {
            AdminRole role = adminRoleMapper.selectById(admin.getRoleId());
            if (role != null) {
                adminVO.setRoleName(role.getName());
                adminVO.setRoleCode(role.getCode());
            }
        }

        // 设置权限信息
        List<String> permissions = getPermissions(admin.getId());
        adminVO.setPermissions(permissions);

        return adminVO;
    }


    @Override
    public List<AdminPermissionTreeVO> getPermissionTree() {
        // 获取所有权限
        List<AdminPermission> permissions = getPermissionList();

        // 构建权限树
        List<AdminPermissionTreeVO> tree = new ArrayList<>();
        Map<Long, AdminPermissionTreeVO> permissionMap = new HashMap<>();

        // 转换为树形VO并建立映射
        for (AdminPermission permission : permissions) {
            AdminPermissionTreeVO treeNode = new AdminPermissionTreeVO();
            BeanUtil.copyProperties(permission, treeNode);
            treeNode.setChildren(new ArrayList<>());
            permissionMap.put(permission.getId(), treeNode);

            // 如果是顶级权限，直接添加到树中
            if (permission.getParentId() == null || permission.getParentId() == 0) {
                tree.add(treeNode);
            }
        }

        // 构建父子关系
        for (AdminPermission permission : permissions) {
            if (permission.getParentId() != null && permission.getParentId() > 0) {
                AdminPermissionTreeVO parent = permissionMap.get(permission.getParentId());
                if (parent != null) {
                    parent.getChildren().add(permissionMap.get(permission.getId()));
                }
            }
        }

        return tree;
    }

    @Override
    public AdminPermission getPermissionById(Long id) {
        return adminPermissionMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createPermission(AdminPermissionDTO permissionDTO) {
        // 检查权限编码是否已存在
        AdminPermission existPermission = adminPermissionMapper.selectByCode(permissionDTO.getCode());
        if (existPermission != null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "权限编码已存在");
        }

        // 检查父权限是否存在
        if (permissionDTO.getParentId() != null && permissionDTO.getParentId() > 0) {
            AdminPermission parentPermission = adminPermissionMapper.selectById(permissionDTO.getParentId());
            if (parentPermission == null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "父权限不存在");
            }
        }

        // 创建权限
        AdminPermission permission = new AdminPermission();
        BeanUtil.copyProperties(permissionDTO, permission);
        permission.setStatus(permissionDTO.getStatus() != null ? permissionDTO.getStatus() : 1);
        permission.setIsSystem(0);
        adminPermissionMapper.insert(permission);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "权限", "创建", "创建权限：" + permission.getName(), getIpAddress(request));

        return permission.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePermission(Long id, AdminPermissionDTO permissionDTO) {
        // 检查权限是否存在
        AdminPermission permission = adminPermissionMapper.selectById(id);
        if (permission == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "权限不存在");
        }

        // 检查是否是系统内置权限
        if (permission.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置权限不能修改");
        }

        // 检查权限编码是否已存在
        if (!permission.getCode().equals(permissionDTO.getCode())) {
            AdminPermission existPermission = adminPermissionMapper.selectByCode(permissionDTO.getCode());
            if (existPermission != null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "权限编码已存在");
            }
        }

        // 检查父权限是否存在
        if (permissionDTO.getParentId() != null && permissionDTO.getParentId() > 0) {
            AdminPermission parentPermission = adminPermissionMapper.selectById(permissionDTO.getParentId());
            if (parentPermission == null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "父权限不存在");
            }

            // 检查是否将自己设为自己的父权限
            if (permissionDTO.getParentId().equals(id)) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "不能将自己设为自己的父权限");
            }

            // 检查是否将自己的子权限设为自己的父权限
            List<Long> childIds = getChildPermissionIds(id);
            if (childIds.contains(permissionDTO.getParentId())) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "不能将子权限设为父权限");
            }
        }

        // 更新权限
        BeanUtil.copyProperties(permissionDTO, permission);
        adminPermissionMapper.updateById(permission);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "权限", "更新", "更新权限：" + permission.getName(), getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermission(Long id) {
        // 检查权限是否存在
        AdminPermission permission = adminPermissionMapper.selectById(id);
        if (permission == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "权限不存在");
        }

        // 检查是否是系统内置权限
        if (permission.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置权限不能删除");
        }

        // 检查是否有子权限
        LambdaQueryWrapper<AdminPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminPermission::getParentId, id);
        Long count = adminPermissionMapper.selectCount(queryWrapper);
        if (count != null && count > 0) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "该权限下有子权限，不能删除");
        }

        // 检查是否有角色使用该权限
        List<Long> roleIds = adminRolePermissionMapper.selectRoleIdsByPermissionId(id);
        if (roleIds != null && !roleIds.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "该权限被角色使用，不能删除");
        }

        // 删除权限
        adminPermissionMapper.deleteById(id);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "权限", "删除", "删除权限：" + permission.getName(), getIpAddress(request));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePermissionStatus(Long id, Integer status) {
        // 检查权限是否存在
        AdminPermission permission = adminPermissionMapper.selectById(id);
        if (permission == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "权限不存在");
        }

        // 检查是否是系统内置权限
        if (permission.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置权限不能修改状态");
        }

        // 更新状态
        permission.setStatus(status);
        adminPermissionMapper.updateById(permission);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        String statusText = status == 1 ? "启用" : "禁用";
        logOperation(currentAdmin.getId(), "权限", statusText, statusText + "权限：" + permission.getName(), getIpAddress(request));

        return true;
    }

    /**
     * 获取子权限ID列表
     *
     * @param parentId 父权限ID
     * @return 子权限ID列表
     */
    private List<Long> getChildPermissionIds(Long parentId) {
        List<Long> childIds = new ArrayList<>();

        // 查询直接子权限
        LambdaQueryWrapper<AdminPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminPermission::getParentId, parentId);
        List<AdminPermission> children = adminPermissionMapper.selectList(queryWrapper);

        // 添加子权限ID
        for (AdminPermission child : children) {
            childIds.add(child.getId());
            // 递归获取子权限的子权限
            childIds.addAll(getChildPermissionIds(child.getId()));
        }

        return childIds;
    }

    @Override
    public IPage<AdminRole> getRoleList(Integer page, Integer pageSize, String keyword) {
        Page<AdminRole> pageParam = new Page<>(page, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<AdminRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdminRole::getIsDeleted, 0);

        // 添加关键字查询
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like(AdminRole::getName, keyword)
                    .or()
                    .like(AdminRole::getCode, keyword));
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(AdminRole::getCreatedAt);

        return adminRoleMapper.selectPage(pageParam, queryWrapper);
    }

    @Override
    public List<Long> getRolePermissionIds(Long roleId) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(roleId);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 获取角色权限ID列表
        return adminRolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRolePermissions(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(roleId);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 检查是否是系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置角色不能修改权限");
        }

        // 删除原有权限
        adminRolePermissionMapper.deleteByRoleId(roleId);

        // 如果权限ID列表为空，则直接返回
        if (permissionIds == null || permissionIds.isEmpty()) {
            return true;
        }

        // 批量插入新权限
        List<AdminRolePermission> rolePermissions = new ArrayList<>();
        for (Long permissionId : permissionIds) {
            AdminRolePermission rolePermission = new AdminRolePermission();
            rolePermission.setRoleId(roleId);
            rolePermission.setPermissionId(permissionId);
            rolePermissions.add(rolePermission);
        }

        // 批量保存
        for (AdminRolePermission rolePermission : rolePermissions) {
            adminRolePermissionMapper.insert(rolePermission);
        }

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        logOperation(currentAdmin.getId(), "角色", "分配权限", "为角色 " + role.getName() + " 分配权限", getIpAddress(request));

        return true;
    }

    /**
     * 根据ID获取角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @Override
    public AdminRole getRoleById(Long id) {
        return adminRoleMapper.selectById(id);
    }

    /**
     * 更新角色状态
     *
     * @param id 角色ID
     * @param status 状态：0-禁用，1-启用
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatus(Long id, Integer status) {
        // 检查角色是否存在
        AdminRole role = adminRoleMapper.selectById(id);
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色不存在");
        }

        // 检查是否是系统内置角色
        if (role.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED, "系统内置角色不能修改状态");
        }

        // 更新状态
        role.setStatus(status);
        adminRoleMapper.updateById(role);

        // 记录操作日志
        AdminVO currentAdmin = getCurrentAdmin();
        String statusText = status == 1 ? "启用" : "禁用";
        logOperation(currentAdmin.getId(), "角色", statusText, statusText + "角色：" + role.getName(), getIpAddress(request));

        return true;
    }

    /**
     * 获取请求IP地址
     *
     * @param request 请求
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
