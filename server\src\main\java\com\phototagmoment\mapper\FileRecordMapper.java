package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.FileInfoDTO;
import com.phototagmoment.entity.FileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文件记录Mapper接口
 */
@Mapper
public interface FileRecordMapper extends BaseMapper<FileRecord> {

    /**
     * 分页查询文件列表
     */
    IPage<FileInfoDTO> selectFileList(Page<FileInfoDTO> page, 
                                      @Param("fileType") String fileType,
                                      @Param("uploaderId") Long uploaderId,
                                      @Param("keyword") String keyword,
                                      @Param("startDate") String startDate,
                                      @Param("endDate") String endDate,
                                      @Param("status") Integer status);

    /**
     * 获取文件详情
     */
    FileInfoDTO selectFileDetail(@Param("fileId") Long fileId);

    /**
     * 搜索文件
     */
    IPage<FileInfoDTO> searchFiles(Page<FileInfoDTO> page,
                                   @Param("keyword") String keyword,
                                   @Param("fileType") String fileType,
                                   @Param("minSize") Long minSize,
                                   @Param("maxSize") Long maxSize,
                                   @Param("status") Integer status);

    /**
     * 获取文件统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalFiles, " +
            "SUM(file_size) as totalSize, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as normalFiles, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as trashFiles, " +
            "COUNT(CASE WHEN uploader_type = 'ADMIN' THEN 1 END) as adminFiles, " +
            "COUNT(CASE WHEN uploader_type = 'USER' THEN 1 END) as userFiles " +
            "FROM ptm_file_record WHERE is_deleted = 0")
    Map<String, Object> selectFileStatistics();

    /**
     * 获取文件类型分布
     */
    @Select("SELECT " +
            "category, " +
            "COUNT(*) as count, " +
            "SUM(file_size) as totalSize " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND status = 0 " +
            "GROUP BY category " +
            "ORDER BY count DESC")
    List<Map<String, Object>> selectFileTypeDistribution();

    /**
     * 获取存储类型分布
     */
    @Select("SELECT " +
            "storage_type, " +
            "COUNT(*) as count, " +
            "SUM(file_size) as totalSize " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND status = 0 " +
            "GROUP BY storage_type")
    List<Map<String, Object>> selectStorageTypeDistribution();

    /**
     * 获取上传统计（按日期）
     */
    @Select("SELECT " +
            "DATE(created_at) as uploadDate, " +
            "COUNT(*) as uploadCount, " +
            "SUM(file_size) as uploadSize " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND created_at >= #{startDate} " +
            "GROUP BY DATE(created_at) " +
            "ORDER BY uploadDate DESC")
    List<Map<String, Object>> selectUploadStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取热门文件（按访问次数）
     */
    @Select("SELECT " +
            "id, original_name, file_path, file_url, file_size, " +
            "access_count, last_access_time, created_at " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND status = 0 " +
            "ORDER BY access_count DESC " +
            "LIMIT #{limit}")
    List<FileInfoDTO> selectPopularFiles(@Param("limit") Integer limit);

    /**
     * 获取最近上传的文件
     */
    @Select("SELECT " +
            "id, original_name, file_path, file_url, file_size, " +
            "uploader_id, uploader_type, created_at " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND status = 0 " +
            "ORDER BY created_at DESC " +
            "LIMIT #{limit}")
    List<FileInfoDTO> selectRecentFiles(@Param("limit") Integer limit);

    /**
     * 获取过期的临时文件
     */
    @Select("SELECT id, file_path FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND is_temp = 1 " +
            "AND temp_expire_time < #{currentTime}")
    List<FileRecord> selectExpiredTempFiles(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新文件状态
     */
    int batchUpdateStatus(@Param("fileIds") List<Long> fileIds, 
                         @Param("status") Integer status,
                         @Param("updatedAt") LocalDateTime updatedAt);

    /**
     * 批量物理删除文件记录
     */
    int batchPhysicalDelete(@Param("fileIds") List<Long> fileIds);

    /**
     * 更新文件访问记录
     */
    int updateFileAccess(@Param("fileId") Long fileId, 
                        @Param("accessTime") LocalDateTime accessTime);

    /**
     * 根据文件路径查询文件记录
     */
    @Select("SELECT * FROM ptm_file_record WHERE file_path = #{filePath} AND is_deleted = 0")
    FileRecord selectByFilePath(@Param("filePath") String filePath);

    /**
     * 根据MD5查询重复文件
     */
    @Select("SELECT * FROM ptm_file_record WHERE md5_hash = #{md5Hash} AND is_deleted = 0")
    List<FileRecord> selectByMd5Hash(@Param("md5Hash") String md5Hash);

    /**
     * 获取用户文件统计
     */
    @Select("SELECT " +
            "uploader_id, " +
            "COUNT(*) as fileCount, " +
            "SUM(file_size) as totalSize " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 AND status = 0 AND uploader_type = #{uploaderType} " +
            "GROUP BY uploader_id " +
            "ORDER BY totalSize DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectUserFileStatistics(@Param("uploaderType") String uploaderType,
                                                       @Param("limit") Integer limit);

    /**
     * 清理回收站文件
     */
    @Select("SELECT id, file_path FROM ptm_file_record " +
            "WHERE status = 1 AND deleted_at < #{beforeTime}")
    List<FileRecord> selectTrashFilesBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取存储空间使用情况
     */
    @Select("SELECT " +
            "storage_type, " +
            "SUM(CASE WHEN status = 0 THEN file_size ELSE 0 END) as usedSize, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as fileCount " +
            "FROM ptm_file_record " +
            "WHERE is_deleted = 0 " +
            "GROUP BY storage_type")
    List<Map<String, Object>> selectStorageUsage();
}
