package com.phototagmoment.controller.admin;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.Result;
import com.phototagmoment.dto.AdminDTO;
import com.phototagmoment.dto.AdminLoginDTO;
import com.phototagmoment.dto.AdminPasswordDTO;
import com.phototagmoment.entity.Admin;
import com.phototagmoment.entity.AdminRole;
import com.phototagmoment.mapper.AdminMapper;
import com.phototagmoment.mapper.AdminRoleMapper;
import com.phototagmoment.security.JwtTokenProvider;
import com.phototagmoment.service.NewAdminService;
import com.phototagmoment.vo.AdminVO;
import com.phototagmoment.vo.LoginResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 新管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/system")
@Tag(name = "系统管理员", description = "系统管理员相关接口")
public class AdminSystemController {

    @Autowired
    private NewAdminService adminService;

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private AdminRoleMapper adminRoleMapper;

    /**
     * 管理员登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @Operation(summary = "管理员登录", description = "管理员登录接口")
    public Result<LoginResultVO> login(@RequestBody @Valid AdminLoginDTO loginDTO) {
        log.info("管理员登录请求: {}", loginDTO.getUsername());
        try {
            // 临时调试代码：打印密码匹配结果
            Admin admin = adminMapper.selectByUsername(loginDTO.getUsername());
            if (admin != null) {
                boolean matches = passwordEncoder.matches(loginDTO.getPassword(), admin.getPassword());
                log.info("密码匹配结果: {}, 输入密码: {}, 数据库密码哈希: {}",
                        matches, loginDTO.getPassword(), admin.getPassword());
            }

            LoginResultVO loginResult = adminService.login(loginDTO);
            log.info("管理员登录成功: {}", loginDTO.getUsername());
            return Result.success(loginResult);
        } catch (Exception e) {
            log.error("管理员登录失败: {}, 错误: {}", loginDTO.getUsername(), e.getMessage());
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 紧急重置管理员密码
     */
    @GetMapping("/emergency-reset")
    @Operation(summary = "紧急重置管理员密码", description = "紧急重置管理员密码为123456")
    public Result<String> emergencyReset() {
        try {
            // 查找管理员
            Admin admin = adminMapper.selectByUsername("admin");
            if (admin == null) {
                return Result.fail("管理员账户不存在");
            }

            // 生成新密码哈希
            String newPassword = "123456";
            String encodedPassword = passwordEncoder.encode(newPassword);

            // 更新密码
            admin.setPassword(encodedPassword);
            admin.setUpdatedAt(LocalDateTime.now()); // 手动设置更新时间
            adminMapper.updateById(admin);

            // 验证新密码
            boolean matches = passwordEncoder.matches(newPassword, admin.getPassword());

            return Result.success("密码重置成功，新密码: " + newPassword + ", 验证结果: " + matches);
        } catch (Exception e) {
            log.error("紧急重置密码失败: {}", e.getMessage(), e);
            return Result.fail("重置失败: " + e.getMessage());
        }
    }

    /**
     * 紧急获取JWT令牌
     */
    @GetMapping("/emergency-token")
    @Operation(summary = "紧急获取JWT令牌", description = "紧急获取管理员JWT令牌，绕过登录验证")
    public Result<LoginResultVO> emergencyToken() {
        try {
            // 查找管理员
            Admin admin = adminMapper.selectByUsername("admin");
            if (admin == null) {
                return Result.fail("管理员账户不存在");
            }

            // 创建UserDetails对象
            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            UserDetails userDetails = new User(admin.getUsername(), admin.getPassword(), authorities);

            // 生成JWT
            String token = jwtTokenProvider.generateToken(userDetails.getUsername());

            // 创建登录结果
            LoginResultVO loginResult = new LoginResultVO();
            loginResult.setToken(token);
            loginResult.setTokenType("Bearer");
            loginResult.setExpiresIn(jwtTokenProvider.getExpiration());

            // 设置管理员信息
            AdminVO adminVO = new AdminVO();
            BeanUtil.copyProperties(admin, adminVO);
            loginResult.setAdmin(adminVO);

            return Result.success(loginResult, "紧急令牌生成成功");
        } catch (Exception e) {
            log.error("紧急令牌生成失败: {}", e.getMessage(), e);
            return Result.fail("令牌生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前管理员信息
     *
     * @return 管理员信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取当前管理员信息", description = "获取当前登录管理员信息")
    public Result<AdminVO> info() {
        log.info("获取当前管理员信息");
        try {
            // 检查当前认证状态
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            log.info("当前认证状态: {}", authentication != null ? authentication.getName() : "null");

            // 从请求头中获取 token
            String authHeader = request.getHeader("Authorization");
            log.info("Authorization 请求头: {}", authHeader);

            if (authentication != null) {
                log.info("当前认证类型: {}", authentication.getClass().getName());
                log.info("当前认证权限: {}", authentication.getAuthorities());
                log.info("当前认证Principal: {}", authentication.getPrincipal());

                // 检查是否是匿名用户
                if ("anonymousUser".equals(authentication.getPrincipal())) {
                    log.warn("当前用户是匿名用户，尝试从请求头获取 token");

                    // 如果是匿名用户，尝试从请求头获取 token
                    if (authHeader != null && authHeader.startsWith("Bearer ")) {
                        String token = authHeader.substring(7);
                        log.info("从请求头获取到 token: {}", token);

                        try {
                            // 验证 token
                            if (jwtTokenProvider.validateToken(token)) {
                                // 从 token 中获取用户名
                                String username = jwtTokenProvider.getUsernameFromToken(token);
                                log.info("从 token 中获取到用户名: {}", username);

                                // 查询管理员信息
                                Admin admin = adminMapper.selectByUsername(username);
                                if (admin != null) {
                                    log.info("查询到管理员信息: {}", admin.getUsername());

                                    // 转换为 VO 并返回
                                    AdminVO adminVO = convertToAdminVO(admin);
                                    log.info("获取当前管理员信息成功: {}", adminVO.getUsername());
                                    return Result.success(adminVO);
                                }
                            }
                        } catch (Exception e) {
                            log.error("验证 token 失败: {}", e.getMessage(), e);
                        }
                    }

                    return Result.fail("未登录或登录已过期，请重新登录");
                }
            } else {
                log.warn("认证信息为空，尝试从请求头获取 token");

                // 如果认证信息为空，尝试从请求头获取 token
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring(7);
                    log.info("从请求头获取到 token: {}", token);

                    try {
                        // 验证 token
                        if (jwtTokenProvider.validateToken(token)) {
                            // 从 token 中获取用户名
                            String username = jwtTokenProvider.getUsernameFromToken(token);
                            log.info("从 token 中获取到用户名: {}", username);

                            // 查询管理员信息
                            Admin admin = adminMapper.selectByUsername(username);
                            if (admin != null) {
                                log.info("查询到管理员信息: {}", admin.getUsername());

                                // 转换为 VO 并返回
                                AdminVO adminVO = convertToAdminVO(admin);
                                log.info("获取当前管理员信息成功: {}", adminVO.getUsername());
                                return Result.success(adminVO);
                            }
                        }
                    } catch (Exception e) {
                        log.error("验证 token 失败: {}", e.getMessage(), e);
                    }
                }

                return Result.fail("未登录或登录已过期，请重新登录");
            }

            AdminVO adminVO = adminService.getCurrentAdmin();
            if (adminVO == null) {
                log.warn("获取当前管理员信息失败: 未找到管理员信息");
                return Result.fail("未找到管理员信息，请重新登录");
            }
            log.info("获取当前管理员信息成功: {}", adminVO.getUsername());
            return Result.success(adminVO);
        } catch (Exception e) {
            log.error("获取当前管理员信息异常: {}", e.getMessage(), e);
            return Result.fail("获取管理员信息失败: " + e.getMessage());
        }
    }

    /**
     * 将 Admin 转换为 AdminVO
     */
    private AdminVO convertToAdminVO(Admin admin) {
        if (admin == null) {
            return null;
        }

        AdminVO adminVO = new AdminVO();
        BeanUtil.copyProperties(admin, adminVO);

        // 设置角色信息
        if (admin.getRoleId() != null) {
            AdminRole role = adminRoleMapper.selectById(admin.getRoleId());
            if (role != null) {
                adminVO.setRoleId(role.getId());
                adminVO.setRoleName(role.getName());
                adminVO.setRoleCode(role.getCode());
            }
        }

        return adminVO;
    }

    /**
     * 管理员登出
     *
     * @return 是否成功
     */
    @PostMapping("/logout")
    @Operation(summary = "管理员登出", description = "管理员登出接口")
    public Result<Boolean> logout() {
        boolean result = adminService.logout();
        return Result.success(result);
    }

    /**
     * 创建管理员
     *
     * @param adminDTO 管理员信息
     * @return 管理员ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建管理员", description = "创建新管理员")
    public Result<Long> createAdmin(@RequestBody @Valid AdminDTO adminDTO) {
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        Long adminId = adminService.createAdmin(adminDTO, currentAdmin.getId());
        return Result.success(adminId);
    }

    /**
     * 更新管理员信息
     *
     * @param id       管理员ID
     * @param adminDTO 管理员信息
     * @return 是否成功
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新管理员信息", description = "更新管理员信息")
    public Result<Boolean> updateAdmin(@Parameter(description = "管理员ID") @PathVariable Long id,
                                      @RequestBody @Valid AdminDTO adminDTO) {
        boolean result = adminService.updateAdmin(id, adminDTO);
        return Result.success(result);
    }

    /**
     * 删除管理员
     *
     * @param id 管理员ID
     * @return 是否成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除管理员", description = "删除管理员")
    public Result<Boolean> deleteAdmin(@Parameter(description = "管理员ID") @PathVariable Long id) {
        boolean result = adminService.deleteAdmin(id);
        return Result.success(result);
    }

    /**
     * 修改管理员密码
     *
     * @param passwordDTO 密码信息
     * @return 是否成功
     */
    @PutMapping("/password")
    @Operation(summary = "修改管理员密码", description = "修改当前管理员密码")
    public Result<Boolean> updatePassword(@RequestBody @Valid AdminPasswordDTO passwordDTO) {
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        boolean result = adminService.updatePassword(currentAdmin.getId(), passwordDTO);
        return Result.success(result);
    }

    /**
     * 重置管理员密码
     *
     * @param id 管理员ID
     * @return 新密码
     */
    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置管理员密码", description = "重置管理员密码")
    public Result<String> resetPassword(@Parameter(description = "管理员ID") @PathVariable Long id) {
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        String newPassword = adminService.resetPassword(id, currentAdmin.getId());
        return Result.success(newPassword);
    }

    /**
     * 启用/禁用管理员
     *
     * @param id     管理员ID
     * @param status 状态（0-禁用，1-启用）
     * @return 是否成功
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "启用/禁用管理员", description = "启用或禁用管理员")
    public Result<Boolean> updateStatus(@Parameter(description = "管理员ID") @PathVariable Long id,
                                       @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam Integer status) {
        AdminVO currentAdmin = adminService.getCurrentAdmin();
        boolean result = adminService.updateStatus(id, status, currentAdmin.getId());
        return Result.success(result);
    }

    /**
     * 分页查询管理员列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 关键字
     * @return 管理员列表
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询管理员列表", description = "分页查询管理员列表")
    public Result<IPage<AdminVO>> getAdminList(@Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
                                              @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
                                              @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        IPage<AdminVO> adminPage = adminService.getAdminPage(page, size, keyword);
        return Result.success(adminPage);
    }

    /**
     * 获取管理员详情
     *
     * @param id 管理员ID
     * @return 管理员详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取管理员详情", description = "获取管理员详情")
    public Result<AdminVO> getAdminDetail(@Parameter(description = "管理员ID") @PathVariable Long id) {
        AdminVO adminVO = adminService.getAdminInfo(id);
        return Result.success(adminVO);
    }
}
