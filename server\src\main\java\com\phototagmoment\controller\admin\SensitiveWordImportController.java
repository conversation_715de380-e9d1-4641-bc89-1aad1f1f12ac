package com.phototagmoment.controller.admin;

import com.phototagmoment.common.Result;
import com.phototagmoment.service.SensitiveWordImportService;
import com.phototagmoment.vo.SensitiveWordImportResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 敏感词导入控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/system/sensitive-word/import")
@Tag(name = "敏感词导入", description = "敏感词导入相关接口")
public class SensitiveWordImportController {

    @Autowired
    private SensitiveWordImportService sensitiveWordImportService;

    /**
     * 导入敏感词
     *
     * @param directoryPath 目录路径，默认为Sensitive-lexicon
     * @return 导入结果
     */
    @PostMapping
    @Operation(summary = "导入敏感词", description = "从指定目录导入敏感词")
    public Result<SensitiveWordImportResultVO> importSensitiveWords(
            @Parameter(description = "目录路径，默认为Sensitive-lexicon") 
            @RequestParam(required = false) String directoryPath) {
        log.info("开始导入敏感词，目录路径: {}", directoryPath);
        
        SensitiveWordImportResultVO result;
        if (directoryPath != null && !directoryPath.isEmpty()) {
            result = sensitiveWordImportService.importSensitiveWords(directoryPath);
        } else {
            result = sensitiveWordImportService.importSensitiveWords();
        }
        
        return Result.success(result);
    }
}
