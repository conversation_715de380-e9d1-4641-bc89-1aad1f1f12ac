<template>
  <div class="file-upload-config-container">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" label-width="80px">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入配置名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="存储类型">
          <el-select v-model="searchForm.storageType" placeholder="请选择" clearable style="width: 150px">
            <el-option label="本地存储" value="LOCAL" />
            <el-option label="七牛云" value="QINIU" />
            <el-option label="阿里云OSS" value="ALIYUN_OSS" />
            <el-option label="腾讯云COS" value="TENCENT_COS" />
            <el-option label="AWS S3" value="AWS_S3" />
            <el-option label="MinIO" value="MINIO" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态">
          <el-select v-model="searchForm.enabled" placeholder="请选择" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="配置状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="正常" :value="0" />
            <el-option label="禁用" :value="1" />
            <el-option label="异常" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="operation-card">
      <div class="operation-bar">
        <div class="left-operations">
          <el-button type="primary" @click="handleCreate">
            <el-icon><plus /></el-icon>
            新增配置
          </el-button>
          <el-button
            type="danger"
            :disabled="selectedConfigs.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><delete /></el-icon>
            批量删除
          </el-button>
          <el-button
            type="warning"
            :disabled="selectedConfigs.length === 0"
            @click="handleBatchTest"
          >
            <el-icon><connection /></el-icon>
            批量测试
          </el-button>
        </div>
        <div class="right-operations">
          <el-button @click="handleRefresh">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="showStatisticsDialog = true">
            <el-icon><data-analysis /></el-icon>
            统计信息
          </el-button>
          <el-button @click="handleRefreshCache">
            <el-icon><refresh-right /></el-icon>
            刷新缓存
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 配置列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="configList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="配置名称" min-width="150">
          <template #default="{ row }">
            <div class="config-name">
              <span>{{ row.configName }}</span>
              <el-tag v-if="row.isDefault" type="success" size="small" style="margin-left: 8px;">
                默认
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="存储类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getStorageTypeTagType(row.storageType)">
              {{ getStorageTypeLabel(row.storageType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="启用状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              @change="handleToggleConfig(row)"
              :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
            />
          </template>
        </el-table-column>
        <el-table-column label="配置状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="最后测试" width="160">
          <template #default="{ row }">
            <div v-if="row.lastTestTime">
              <div>{{ formatDateTime(row.lastTestTime) }}</div>
              <el-tag
                :type="getTestResultTagType(row.lastTestResult)"
                size="small"
              >
                {{ getTestResultLabel(row.lastTestResult) }}
              </el-tag>
            </div>
            <span v-else class="text-muted">未测试</span>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="200">
          <template #default="{ row }">
            <span class="description" :title="row.description">
              {{ row.description || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">
              <el-icon><view /></el-icon>
              查看
            </el-button>
            <el-button
              size="small"
              @click="handleEdit(row)"
              :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')"
            >
              <el-icon><edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" @click="handleTest(row)">
              <el-icon><connection /></el-icon>
              测试
            </el-button>
            <el-dropdown @command="(command) => handleMoreAction(command, row)">
              <el-button size="small">
                更多<el-icon><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="setDefault"
                    :disabled="row.isDefault || !row.enabled || (!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN'))"
                  >
                    设为默认
                  </el-dropdown-item>
                  <el-dropdown-item command="copy" :disabled="!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')">
                    复制配置
                  </el-dropdown-item>
                  <el-dropdown-item command="export">导出配置</el-dropdown-item>
                  <el-dropdown-item
                    command="delete"
                    divided
                    style="color: #f56c6c;"
                    :disabled="row.isDefault || (!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN'))"
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑配置对话框 -->
    <ConfigFormDialog
      v-model="showCreateDialog"
      :config="currentConfig"
      :is-edit="isEdit"
      @success="handleFormSuccess"
    />

    <!-- 配置详情对话框 -->
    <ConfigDetailDialog
      v-model="showDetailDialog"
      :config="currentConfig"
    />

    <!-- 统计信息对话框 -->
    <ConfigStatisticsDialog
      v-model="showStatisticsDialog"
    />

    <!-- 测试结果对话框 -->
    <TestResultDialog
      v-model="showTestDialog"
      :test-result="testResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Connection,
  DataAnalysis,
  RefreshRight,
  View,
  Edit,
  ArrowDown
} from '@element-plus/icons-vue'
import {
  getConfigList,
  deleteConfig,
  batchDeleteConfigs,
  toggleConfig,
  setDefaultConfig,
  testConfig,
  batchTestConfigs,
  copyConfig,
  refreshConfigCache,
  type FileUploadConfig,
  type ConfigListParams,
  type TestResult
} from '@/api/fileUploadConfig'
import ConfigFormDialog from './components/ConfigFormDialog.vue'
import ConfigDetailDialog from './components/ConfigDetailDialog.vue'
import ConfigStatisticsDialog from './components/ConfigStatisticsDialog.vue'
import TestResultDialog from './components/TestResultDialog.vue'
import { formatDateTime } from '@/utils/date'
import { hasPermission } from '@/utils/permission'

// 响应式数据
const loading = ref(false)
const configList = ref<FileUploadConfig[]>([])
const selectedConfigs = ref<FileUploadConfig[]>([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showStatisticsDialog = ref(false)
const showTestDialog = ref(false)
const isEdit = ref(false)
const currentConfig = ref<FileUploadConfig | null>(null)
const testResult = ref<TestResult | null>(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  storageType: '',
  enabled: undefined,
  status: undefined
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 生命周期
onMounted(() => {
  console.log('文件上传配置页面已挂载')
  console.log('当前用户权限:', hasPermission('ADMIN'), hasPermission('SUPER_ADMIN'))
  loadConfigList()
})

// 方法
const loadConfigList = async () => {
  loading.value = true
  try {
    const params: ConfigListParams = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await getConfigList(params)
    if ((response as any).code === 200) {
      configList.value = (response as any).data.records || []
      pagination.total = (response as any).data.total || 0
    } else {
      ElMessage.error((response as any).message || '获取配置列表失败')
      configList.value = []
      pagination.total = 0
    }
  } catch (error: any) {
    console.error('加载配置列表失败:', error)

    // 根据错误类型提供更具体的错误信息
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法访问配置列表')
    } else if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
    } else {
      ElMessage.error(error.response?.data?.message || '加载配置列表失败')
    }

    configList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadConfigList()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    storageType: '',
    enabled: undefined,
    status: undefined
  })
  pagination.page = 1
  loadConfigList()
}

const handleRefresh = () => {
  loadConfigList()
}

const handleSelectionChange = (selection: FileUploadConfig[]) => {
  selectedConfigs.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadConfigList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadConfigList()
}

const handleCreate = () => {
  // 清空当前配置，确保新增时表单为空白状态
  currentConfig.value = null
  isEdit.value = false
  showCreateDialog.value = true
}

const handleView = (config: FileUploadConfig) => {
  currentConfig.value = config
  showDetailDialog.value = true
}

const handleEdit = (config: FileUploadConfig) => {
  currentConfig.value = { ...config }
  isEdit.value = true
  showCreateDialog.value = true
}

const handleTest = async (config: FileUploadConfig) => {
  try {
    const response = await testConfig(config.id!)
    if ((response as any).code === 200) {
      testResult.value = (response as any).data
      showTestDialog.value = true
      loadConfigList() // 刷新列表以更新测试结果
    }
  } catch (error) {
    console.error('测试配置失败:', error)
    ElMessage.error('测试配置失败')
  }
}

const handleToggleConfig = async (config: FileUploadConfig) => {
  // 检查权限
  if (!hasPermission('ADMIN') && !hasPermission('SUPER_ADMIN')) {
    ElMessage.warning('您没有权限执行此操作')
    config.enabled = !config.enabled // 恢复原状态
    return
  }

  const originalState = !config.enabled // 保存原始状态

  try {
    const response = await toggleConfig(config.id!, config.enabled)
    if ((response as any).code === 200) {
      ElMessage.success((response as any).message || `配置已${config.enabled ? '启用' : '禁用'}`)
      // 刷新列表以获取最新状态
      loadConfigList()
    } else {
      // 恢复原状态
      config.enabled = originalState
      ElMessage.error((response as any).message || '操作失败')
    }
  } catch (error: any) {
    // 恢复原状态
    config.enabled = originalState
    console.error('切换配置状态失败:', error)

    // 根据错误类型提供更具体的错误信息
    if (error.response?.status === 403) {
      ElMessage.error('权限不足，无法执行此操作')
    } else if (error.response?.status === 404) {
      ElMessage.error('配置不存在')
    } else {
      ElMessage.error(error.response?.data?.message || '切换配置状态失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedConfigs.value.length === 0) {
    ElMessage.warning('请选择要删除的配置')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedConfigs.value.length} 个配置吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const configIds = selectedConfigs.value.map(config => config.id!)
    const response = await batchDeleteConfigs(configIds)

    if ((response as any).code === 200) {
      ElMessage.success('删除成功')
      loadConfigList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除配置失败:', error)
      ElMessage.error('批量删除配置失败')
    }
  }
}

const handleBatchTest = async () => {
  if (selectedConfigs.value.length === 0) {
    ElMessage.warning('请选择要测试的配置')
    return
  }

  try {
    const configIds = selectedConfigs.value.map(config => config.id!)
    const response = await batchTestConfigs(configIds)

    if ((response as any).code === 200) {
      ElMessage.success('批量测试完成')
      loadConfigList()
    }
  } catch (error) {
    console.error('批量测试配置失败:', error)
    ElMessage.error('批量测试配置失败')
  }
}

const handleRefreshCache = async () => {
  try {
    const response = await refreshConfigCache()
    if ((response as any).code === 200) {
      ElMessage.success('缓存刷新成功')
    }
  } catch (error) {
    console.error('刷新缓存失败:', error)
    ElMessage.error('刷新缓存失败')
  }
}

const handleMoreAction = async (command: string, config: FileUploadConfig) => {
  switch (command) {
    case 'setDefault':
      await handleSetDefault(config)
      break
    case 'copy':
      await handleCopy(config)
      break
    case 'export':
      handleExport(config)
      break
    case 'delete':
      await handleDelete(config)
      break
  }
}

const handleSetDefault = async (config: FileUploadConfig) => {
  try {
    const response = await setDefaultConfig(config.id!)
    if ((response as any).code === 200) {
      ElMessage.success('设置默认配置成功')
      loadConfigList()
    }
  } catch (error) {
    console.error('设置默认配置失败:', error)
    ElMessage.error('设置默认配置失败')
  }
}

const handleCopy = async (config: FileUploadConfig) => {
  try {
    const newConfigName = await ElMessageBox.prompt(
      '请输入新配置名称',
      '复制配置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.{1,50}$/,
        inputErrorMessage: '配置名称长度应在1-50个字符之间'
      }
    )

    const response = await copyConfig(config.id!, newConfigName.value)
    if ((response as any).code === 200) {
      ElMessage.success('配置复制成功')
      loadConfigList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制配置失败:', error)
      ElMessage.error('复制配置失败')
    }
  }
}

const handleExport = (config: FileUploadConfig) => {
  // TODO: 实现配置导出功能
  console.log('导出配置:', config.configName)
  ElMessage.info('导出功能开发中...')
}

const handleDelete = async (config: FileUploadConfig) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.configName}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteConfig(config.id!)
    if ((response as any).code === 200) {
      ElMessage.success('配置删除成功')
      loadConfigList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  currentConfig.value = null
  isEdit.value = false
  loadConfigList()
}

// 工具方法
const getStorageTypeTagType = (storageType: string) => {
  const typeMap = {
    LOCAL: 'info',
    QINIU: 'success',
    ALIYUN_OSS: 'warning',
    TENCENT_COS: 'primary',
    AWS_S3: 'danger',
    MINIO: 'info'
  }
  return typeMap[storageType] || 'info'
}

const getStorageTypeLabel = (storageType: string) => {
  const labelMap = {
    LOCAL: '本地存储',
    QINIU: '七牛云',
    ALIYUN_OSS: '阿里云OSS',
    TENCENT_COS: '腾讯云COS',
    AWS_S3: 'AWS S3',
    MINIO: 'MinIO'
  }
  return labelMap[storageType] || '未知'
}

const getStatusTagType = (status: number) => {
  const typeMap = {
    0: 'success',
    1: 'warning',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: number) => {
  const labelMap = {
    0: '正常',
    1: '禁用',
    2: '异常'
  }
  return labelMap[status] || '未知'
}

const getTestResultTagType = (testResult: string) => {
  if (!testResult) return 'info'
  try {
    const result = JSON.parse(testResult)
    return result.success ? 'success' : 'danger'
  } catch {
    return 'info'
  }
}

const getTestResultLabel = (testResult: string) => {
  if (!testResult) return '未测试'
  try {
    const result = JSON.parse(testResult)
    return result.success ? '成功' : '失败'
  } catch {
    return '未知'
  }
}
</script>

<style scoped>
.file-upload-config-container {
  padding: 20px;
}

.search-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-operations,
.right-operations {
  display: flex;
  gap: 8px;
}

.config-name {
  display: flex;
  align-items: center;
}

.description {
  display: block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
